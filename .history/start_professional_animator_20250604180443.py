#!/usr/bin/env python3
"""
专业游戏动画师系统启动脚本
Professional Game Animator System Startup Script
"""

import subprocess
import sys
import os
import time
from pathlib import Path


def check_dependencies():
    """检查依赖项"""
    print("🔍 检查系统依赖...")
    
    required_packages = [
        'fastapi', 'uvicorn', 'pydantic', 'loguru',
        'spacy', 'transformers', 'torch', 'numpy',
        'langchain', 'langgraph'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}")
    
    if missing_packages:
        print(f"\n🚫 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: poetry install")
        return False
    
    print("✅ 所有依赖项检查通过")
    return True


def check_blender():
    """检查Blender可用性"""
    print("\n🎨 检查Blender...")
    
    blender_paths = [
        "/Applications/Blender.app/Contents/MacOS/Blender",  # macOS
        "/usr/bin/blender",  # Linux
        "C:\\Program Files\\Blender Foundation\\Blender\\blender.exe",  # Windows
        "blender"  # PATH中
    ]
    
    for path in blender_paths:
        if os.path.exists(path):
            print(f"✅ 找到Blender: {path}")
            return True
    
    # 尝试从PATH运行
    try:
        result = subprocess.run(['blender', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ Blender在系统PATH中可用")
            return True
    except (subprocess.TimeoutExpired, FileNotFoundError):
        pass
    
    print("⚠️  未找到Blender，某些功能可能不可用")
    print("请安装Blender: https://www.blender.org/download/")
    return False


def setup_directories():
    """设置必要的目录"""
    print("\n📁 设置目录结构...")
    
    directories = [
        'output/animations',
        'logs',
        'temp/animation_data'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ {directory}")


def run_tests():
    """运行系统测试"""
    print("\n🧪 运行系统测试...")
    
    try:
        result = subprocess.run([
            sys.executable, 'test_professional_animator.py'
        ], capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ 系统测试通过")
            return True
        else:
            print("❌ 系统测试失败")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ 测试超时")
        return False
    except Exception as e:
        print(f"💥 测试执行错误: {e}")
        return False


def start_server():
    """启动API服务器"""
    print("\n🚀 启动专业动画师API服务器...")
    print("=" * 50)
    
    try:
        # 使用uvicorn启动服务器
        cmd = [
            sys.executable, '-m', 'uvicorn',
            'backend.app:app',
            '--host', '0.0.0.0',
            '--port', '8000',
            '--reload',
            '--log-level', 'info'
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        print("\n🌐 服务器将在以下地址启动:")
        print("   - API文档: http://localhost:8000/docs")
        print("   - ReDoc: http://localhost:8000/redoc")
        print("   - 专业动画师: http://localhost:8000/animation/")
        print("\n按 Ctrl+C 停止服务器")
        print("=" * 50)
        
        # 启动服务器
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n\n👋 服务器已停止")
    except Exception as e:
        print(f"\n💥 启动服务器失败: {e}")


def show_usage_examples():
    """显示使用示例"""
    print("\n📖 使用示例:")
    print("=" * 30)
    
    examples = [
        {
            "title": "🎪 复杂特技动作",
            "text": "生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步",
            "level": "intermediate"
        },
        {
            "title": "⚔️ 战斗动作",
            "text": "快速冲刺攻击，然后防御姿态",
            "level": "intermediate"
        },
        {
            "title": "🚶 基础移动",
            "text": "慢慢走向前方，然后挥手打招呼",
            "level": "junior"
        }
    ]
    
    for example in examples:
        print(f"\n{example['title']}:")
        print(f"curl -X POST 'http://localhost:8000/animation/generate' \\")
        print(f"     -H 'Content-Type: application/json' \\")
        print(f"     -d '{{")
        print(f"       \"text\": \"{example['text']}\",")
        print(f"       \"character_id\": \"test_character\",")
        print(f"       \"animator_level\": \"{example['level']}\"")
        print(f"     }}'")


def main():
    """主函数"""
    print("🎬 专业游戏动画师系统启动器")
    print("=" * 60)
    print("Professional Game Animator System Launcher")
    print("=" * 60)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        sys.exit(1)
    
    print(f"✅ Python版本: {sys.version}")
    
    # 检查依赖
    if not check_dependencies():
        print("\n💡 安装依赖:")
        print("   poetry install")
        print("   # 或者")
        print("   pip install -r backend/requirements.txt")
        sys.exit(1)
    
    # 检查Blender
    blender_available = check_blender()
    
    # 设置目录
    setup_directories()
    
    # 询问是否运行测试
    print("\n❓ 是否运行系统测试? (y/n): ", end="")
    try:
        choice = input().lower().strip()
        if choice in ['y', 'yes', '是']:
            if not run_tests():
                print("⚠️  测试失败，但仍可继续启动服务器")
                print("❓ 是否继续? (y/n): ", end="")
                choice = input().lower().strip()
                if choice not in ['y', 'yes', '是']:
                    sys.exit(1)
    except KeyboardInterrupt:
        print("\n👋 用户取消")
        sys.exit(0)
    
    # 显示使用示例
    show_usage_examples()
    
    # 启动服务器
    print("\n❓ 是否启动API服务器? (y/n): ", end="")
    try:
        choice = input().lower().strip()
        if choice in ['y', 'yes', '是']:
            start_server()
        else:
            print("\n💡 手动启动服务器:")
            print("   poetry run uvicorn backend.app:app --reload")
            print("   # 或者")
            print("   python -m uvicorn backend.app:app --reload")
    except KeyboardInterrupt:
        print("\n👋 用户取消")
        sys.exit(0)


if __name__ == "__main__":
    main()
