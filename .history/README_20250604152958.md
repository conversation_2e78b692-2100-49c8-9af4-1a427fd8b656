# Motion Agent

A Natural Language to 3D Animation Generation System that converts text descriptions into animated 3D character movements using FastAPI backend and Blender for animation generation.

## Project Structure

```
motion-agent/
├── backend/
│   ├── app.py                   # FastAPI服务入口
│   ├── nlu/
│   │   ├── __init__.py
│   │   ├── nlu_pipeline.py      # 自然语言解析主流程
│   │   ├── models.py            # 动作数据结构定义
│   │   └── utils.py             # 工具函数（解析辅助等）
│   ├── requirements.txt         # 依赖清单
│   └── poetry.lock
├── blender_scripts/
│   └── generate_animation.py   # Blender Python脚本，接收动作指令，生成动画并导出FBX
├── pyproject.toml              # poetry 配置文件
└── README.md
```

## Features

- **Natural Language Processing**: Convert text descriptions into structured motion data
- **Action Recognition**: Identify and parse various types of movements (locomotion, gestures, poses, etc.)
- **Animation Generation**: Generate 3D animations using Blender Python API
- **RESTful API**: FastAPI-based backend for easy integration
- **Flexible Export**: Support for FBX and other animation formats
- **Advanced Logging**: Structured logging with Loguru for better debugging and monitoring
- **LangChain Integration**: Advanced NLP capabilities with LangChain framework
- **LangGraph Workflows**: Structured AI workflows for complex motion understanding
- **Code Quality**: Ruff for fast Python linting and formatting

## Installation

### Prerequisites

- Python 3.8+
- Blender 3.0+ (for animation generation)
- Poetry (recommended) or pip

### Using Poetry (Recommended)

```bash
# Clone the repository
git clone <repository-url>
cd motion-agent

# Install dependencies
poetry install

# Activate virtual environment
poetry shell
```

### Using pip

```bash
# Clone the repository
git clone <repository-url>
cd motion-agent

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r backend/requirements.txt
```

## Usage

### Starting the API Server

```bash
# Using Poetry
poetry run uvicorn backend.app:app --reload --host 0.0.0.0 --port 8000

# Using Python directly
cd backend
python app.py
```

The API will be available at `http://localhost:8000`

### API Documentation

Once the server is running, visit:
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

### Example API Usage

```bash
# Generate motion from text
curl -X POST "http://localhost:8000/generate-motion" \
     -H "Content-Type: application/json" \
     -d '{
       "text": "Walk forward slowly and then wave your right hand",
       "character_id": "character_01"
     }'
```

### Using Blender Script

```bash
# Run Blender script with motion data
blender --background --python blender_scripts/generate_animation.py -- \
        --input motion_data.json \
        --output animation.fbx \
        --format fbx
```

## Supported Actions

### Locomotion
- walk, run, jump, hop, skip, march, stride

### Poses
- sit, stand, crouch, kneel, lie, lean, bend

### Gestures
- wave, point, clap, nod, shake, bow, salute

### Interactions
- pick, grab, hold, carry, push, pull, throw, catch

### Expressions
- smile, frown, blink

## Configuration

### Environment Variables

Create a `.env` file in the project root:

```env
# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=true

# Blender Configuration
BLENDER_PATH=/path/to/blender
DEFAULT_CHARACTER_MODEL=/path/to/character.blend

# Animation Settings
DEFAULT_FRAME_RATE=24
DEFAULT_ANIMATION_QUALITY=medium

# Logging Configuration
LOG_LEVEL=INFO
LOG_ROTATION=10 MB
LOG_RETENTION=7 days
```

### Logging

The application uses Loguru for structured logging with the following features:

- **Console Output**: Colored, formatted logs for development
- **File Logging**: Automatic log rotation and retention in `logs/motion_agent.log`
- **Log Levels**: DEBUG, INFO, WARNING, ERROR, SUCCESS
- **Structured Format**: Includes timestamp, level, module, function, and line number

Log files are automatically rotated when they reach 10MB and kept for 7 days by default.

## Development

### Running Tests

```bash
# Using Poetry
poetry run pytest

# Using pip
pytest
```

### Code Formatting and Linting

```bash
# Format code with Black
poetry run black backend/

# Lint and format with Ruff (faster alternative)
poetry run ruff check backend/
poetry run ruff format backend/

# Fix issues automatically with Ruff
poetry run ruff check --fix backend/

# Lint with flake8 (legacy)
poetry run flake8 backend/

# Type checking with mypy
poetry run mypy backend/
```

### Ruff Configuration

Ruff is configured for:
- **Fast linting**: Much faster than flake8
- **Auto-fixing**: Automatically fix many issues
- **Black compatibility**: Same formatting rules as Black
- **Comprehensive rules**: Includes pycodestyle, Pyflakes, isort, and more

Configuration is in `pyproject.toml` and `.ruff.toml`.

### Pre-commit Hooks

```bash
# Install pre-commit hooks
poetry run pre-commit install

# Run hooks manually
poetry run pre-commit run --all-files
```

## API Endpoints

### POST /generate-motion

Generate animation from natural language description using the basic NLU pipeline.

**Request Body:**
```json
{
  "text": "Walk forward slowly and then wave your right hand",
  "character_id": "character_01"
}
```

### POST /generate-motion-advanced

Generate animation using the advanced LangGraph pipeline with enhanced NLP capabilities.

**Request Body:**
```json
{
  "text": "Walk forward slowly and then wave your right hand",
  "character_id": "character_01",
  "use_langgraph": true,
  "context": {
    "environment": "indoor",
    "mood": "happy"
  }
}
```

**Response:**
```json
{
  "success": true,
  "action_sequence": {
    "id": "seq_123",
    "actions": [
      {
        "id": "act_1",
        "type": "locomotion",
        "name": "walk",
        "duration": 3.0,
        "start_time": 0.0,
        "parameters": {
          "speed": "slow",
          "direction": "forward"
        }
      }
    ],
    "total_duration": 3.0,
    "character_id": "character_01"
  },
  "blender_script_path": "blender_scripts/generate_animation.py"
}
```

### GET /health

Health check endpoint.

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- FastAPI for the excellent web framework
- Blender for the powerful 3D animation capabilities
- NLTK for natural language processing tools
