"""
自然语言解析主流程
Natural Language Understanding Pipeline for Motion Generation
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional
import re
import json

from .models import MotionRequest, MotionResponse, ActionSequence, Action, ActionType
from .utils import extract_motion_keywords, parse_duration, normalize_text

logger = logging.getLogger(__name__)


class NLUPipeline:
    """
    Main NLU pipeline for processing natural language motion descriptions
    """
    
    def __init__(self):
        self.action_patterns = self._load_action_patterns()
        self.body_parts = {
            'hand', 'hands', 'arm', 'arms', 'leg', 'legs', 'head', 'body',
            'foot', 'feet', 'shoulder', 'shoulders', 'hip', 'hips'
        }
        
    def _load_action_patterns(self) -> Dict[str, ActionType]:
        """Load predefined action patterns and their mappings"""
        return {
            # Basic movements
            'walk': ActionType.LOCOMOTION,
            'run': ActionType.LOCOMOTION,
            'jump': ActionType.LOCOMOTION,
            'sit': ActionType.POSE,
            'stand': ActionType.POSE,
            'crouch': ActionType.POSE,
            'kneel': ActionType.POSE,
            
            # Gestures
            'wave': ActionType.GESTURE,
            'point': ActionType.GESTURE,
            'clap': ActionType.GESTURE,
            'nod': ActionType.GESTURE,
            'shake': ActionType.GESTURE,
            
            # Interactions
            'pick': ActionType.INTERACTION,
            'grab': ActionType.INTERACTION,
            'push': ActionType.INTERACTION,
            'pull': ActionType.INTERACTION,
            'throw': ActionType.INTERACTION,
            
            # Expressions
            'smile': ActionType.EXPRESSION,
            'frown': ActionType.EXPRESSION,
            'blink': ActionType.EXPRESSION,
        }
    
    async def process(self, request: MotionRequest) -> MotionResponse:
        """
        Main processing pipeline
        """
        try:
            # Step 1: Normalize and preprocess text
            normalized_text = normalize_text(request.text)
            
            # Step 2: Extract motion keywords and context
            motion_keywords = extract_motion_keywords(normalized_text)
            
            # Step 3: Parse actions from text
            actions = await self._parse_actions(normalized_text, motion_keywords)
            
            # Step 4: Create action sequence
            action_sequence = ActionSequence(
                actions=actions,
                total_duration=sum(action.duration for action in actions),
                character_id=request.character_id
            )
            
            # Step 5: Generate response
            response = MotionResponse(
                success=True,
                action_sequence=action_sequence,
                original_text=request.text,
                processed_text=normalized_text,
                blender_script_path="blender_scripts/generate_animation.py"
            )
            
            return response
            
        except Exception as e:
            logger.error(f"Error processing motion request: {str(e)}")
            return MotionResponse(
                success=False,
                error_message=str(e),
                original_text=request.text
            )
    
    async def _parse_actions(self, text: str, keywords: List[str]) -> List[Action]:
        """
        Parse individual actions from the processed text
        """
        actions = []
        
        # Simple pattern matching for now - can be enhanced with ML models
        for keyword in keywords:
            if keyword in self.action_patterns:
                action_type = self.action_patterns[keyword]
                
                # Extract duration if specified
                duration = parse_duration(text, keyword)
                
                # Extract target body parts
                target_parts = self._extract_body_parts(text, keyword)
                
                # Create action
                action = Action(
                    type=action_type,
                    name=keyword,
                    duration=duration,
                    parameters={
                        'target_parts': target_parts,
                        'intensity': self._extract_intensity(text, keyword),
                        'direction': self._extract_direction(text, keyword)
                    }
                )
                
                actions.append(action)
        
        # If no actions found, create a default idle action
        if not actions:
            actions.append(Action(
                type=ActionType.POSE,
                name="idle",
                duration=2.0,
                parameters={}
            ))
        
        return actions
    
    def _extract_body_parts(self, text: str, action_keyword: str) -> List[str]:
        """Extract mentioned body parts related to the action"""
        found_parts = []
        text_lower = text.lower()
        
        for part in self.body_parts:
            if part in text_lower:
                found_parts.append(part)
        
        return found_parts
    
    def _extract_intensity(self, text: str, action_keyword: str) -> str:
        """Extract intensity modifiers (slow, fast, gentle, etc.)"""
        intensity_words = {
            'slow': 'slow',
            'slowly': 'slow',
            'fast': 'fast',
            'quickly': 'fast',
            'gentle': 'gentle',
            'gently': 'gentle',
            'hard': 'strong',
            'strong': 'strong',
            'soft': 'gentle'
        }
        
        text_lower = text.lower()
        for word, intensity in intensity_words.items():
            if word in text_lower:
                return intensity
        
        return 'normal'
    
    def _extract_direction(self, text: str, action_keyword: str) -> Optional[str]:
        """Extract directional information"""
        directions = ['left', 'right', 'up', 'down', 'forward', 'backward', 'north', 'south', 'east', 'west']
        text_lower = text.lower()
        
        for direction in directions:
            if direction in text_lower:
                return direction
        
        return None
