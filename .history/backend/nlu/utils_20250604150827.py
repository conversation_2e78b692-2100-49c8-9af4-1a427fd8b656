"""
工具函数（解析辅助等）
Utility functions for NLU processing
"""

import re
import string
from typing import List, Dict, Any, Optional, Tuple
import nltk
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize
import logging

logger = logging.getLogger(__name__)

# Download required NLTK data (run once)
try:
    nltk.data.find('tokenizers/punkt')
    nltk.data.find('corpora/stopwords')
except LookupError:
    nltk.download('punkt')
    nltk.download('stopwords')


def normalize_text(text: str) -> str:
    """
    Normalize input text for processing
    """
    # Convert to lowercase
    text = text.lower().strip()
    
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text)
    
    # Remove punctuation except periods and commas that might indicate sequence
    text = text.translate(str.maketrans('', '', string.punctuation.replace('.', '').replace(',', '')))
    
    return text


def extract_motion_keywords(text: str) -> List[str]:
    """
    Extract motion-related keywords from text
    """
    # Define motion-related keywords
    motion_verbs = {
        'walk', 'run', 'jump', 'hop', 'skip', 'march', 'stride',
        'sit', 'stand', 'crouch', 'kneel', 'lie', 'lean', 'bend',
        'wave', 'point', 'clap', 'nod', 'shake', 'bow', 'salute',
        'pick', 'grab', 'hold', 'carry', 'push', 'pull', 'throw', 'catch',
        'turn', 'rotate', 'spin', 'twist', 'move', 'step', 'dance',
        'raise', 'lower', 'lift', 'drop', 'reach', 'stretch', 'extend'
    }
    
    # Tokenize text
    tokens = word_tokenize(text)
    
    # Extract motion keywords
    keywords = []
    for token in tokens:
        if token in motion_verbs:
            keywords.append(token)
    
    return keywords


def parse_duration(text: str, action_keyword: str) -> float:
    """
    Parse duration information from text
    Returns duration in seconds
    """
    # Look for explicit time mentions
    time_patterns = [
        r'(\d+(?:\.\d+)?)\s*seconds?',
        r'(\d+(?:\.\d+)?)\s*secs?',
        r'(\d+(?:\.\d+)?)\s*minutes?',
        r'(\d+(?:\.\d+)?)\s*mins?',
    ]
    
    for pattern in time_patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            duration = float(match.group(1))
            if 'minute' in match.group(0) or 'min' in match.group(0):
                duration *= 60  # Convert minutes to seconds
            return duration
    
    # Look for relative duration words
    duration_words = {
        'quickly': 0.5,
        'fast': 0.8,
        'slowly': 3.0,
        'slow': 2.5,
        'briefly': 1.0,
        'long': 4.0,
        'short': 1.5,
    }
    
    text_lower = text.lower()
    for word, duration in duration_words.items():
        if word in text_lower:
            return duration
    
    # Default duration based on action type
    default_durations = {
        'walk': 2.0,
        'run': 1.5,
        'jump': 1.0,
        'wave': 1.5,
        'sit': 2.0,
        'stand': 1.0,
        'nod': 0.8,
        'clap': 1.0,
    }
    
    return default_durations.get(action_keyword, 2.0)


def extract_sequence_order(text: str) -> List[Tuple[str, int]]:
    """
    Extract sequence order from text using temporal indicators
    Returns list of (action, order) tuples
    """
    # Temporal indicators
    sequence_words = {
        'first': 1, 'initially': 1, 'start': 1, 'begin': 1,
        'then': 2, 'next': 2, 'after': 2, 'afterwards': 2,
        'finally': 3, 'lastly': 3, 'end': 3, 'conclude': 3,
        'second': 2, 'third': 3, 'fourth': 4, 'fifth': 5
    }
    
    # Split text into sentences/clauses
    clauses = re.split(r'[,.;]|\sand\s|\sthen\s', text)
    
    sequence = []
    for i, clause in enumerate(clauses):
        clause = clause.strip()
        if not clause:
            continue
            
        # Extract motion keywords from this clause
        keywords = extract_motion_keywords(clause)
        
        # Determine order
        order = i + 1  # Default sequential order
        for word, seq_order in sequence_words.items():
            if word in clause.lower():
                order = seq_order
                break
        
        for keyword in keywords:
            sequence.append((keyword, order))
    
    return sequence


def extract_spatial_relationships(text: str) -> Dict[str, Any]:
    """
    Extract spatial relationships and directions from text
    """
    spatial_info = {
        'directions': [],
        'locations': [],
        'distances': [],
        'relative_positions': []
    }
    
    # Direction words
    directions = ['left', 'right', 'up', 'down', 'forward', 'backward', 
                 'north', 'south', 'east', 'west', 'towards', 'away']
    
    # Location words
    locations = ['here', 'there', 'center', 'corner', 'edge', 'middle',
                'front', 'back', 'side', 'top', 'bottom']
    
    # Distance words
    distance_pattern = r'(\d+(?:\.\d+)?)\s*(meters?|feet|steps?|inches?)'
    
    text_lower = text.lower()
    
    # Extract directions
    for direction in directions:
        if direction in text_lower:
            spatial_info['directions'].append(direction)
    
    # Extract locations
    for location in locations:
        if location in text_lower:
            spatial_info['locations'].append(location)
    
    # Extract distances
    distance_matches = re.findall(distance_pattern, text_lower)
    for value, unit in distance_matches:
        spatial_info['distances'].append({'value': float(value), 'unit': unit})
    
    return spatial_info


def validate_action_sequence(actions: List[Dict[str, Any]]) -> Tuple[bool, List[str]]:
    """
    Validate that an action sequence is physically possible
    Returns (is_valid, list_of_issues)
    """
    issues = []
    
    # Check for conflicting simultaneous actions
    for i, action in enumerate(actions):
        for j, other_action in enumerate(actions[i+1:], i+1):
            if (action.get('start_time', 0) < other_action.get('start_time', 0) + other_action.get('duration', 0) and
                other_action.get('start_time', 0) < action.get('start_time', 0) + action.get('duration', 0)):
                
                # Check if actions conflict
                if _actions_conflict(action, other_action):
                    issues.append(f"Conflicting actions: {action.get('name')} and {other_action.get('name')}")
    
    # Check for impossible transitions
    for i in range(len(actions) - 1):
        current = actions[i]
        next_action = actions[i + 1]
        
        if not _is_valid_transition(current, next_action):
            issues.append(f"Invalid transition from {current.get('name')} to {next_action.get('name')}")
    
    return len(issues) == 0, issues


def _actions_conflict(action1: Dict[str, Any], action2: Dict[str, Any]) -> bool:
    """Check if two actions conflict with each other"""
    # Simple conflict detection - can be enhanced
    conflicting_pairs = [
        ('sit', 'stand'),
        ('run', 'sit'),
        ('jump', 'crouch'),
    ]
    
    name1 = action1.get('name', '')
    name2 = action2.get('name', '')
    
    for pair in conflicting_pairs:
        if (name1 in pair and name2 in pair) or (name2 in pair and name1 in pair):
            return True
    
    return False


def _is_valid_transition(from_action: Dict[str, Any], to_action: Dict[str, Any]) -> bool:
    """Check if transition between two actions is physically possible"""
    # Simple validation - can be enhanced with more sophisticated rules
    impossible_transitions = [
        ('sit', 'jump'),  # Can't jump directly from sitting
        ('lie', 'run'),   # Can't run directly from lying down
    ]
    
    from_name = from_action.get('name', '')
    to_name = to_action.get('name', '')
    
    return (from_name, to_name) not in impossible_transitions
