"""
FastAPI服务入口
Motion Agent Backend API Server
"""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any
import uvicorn

from nlu.nlu_pipeline import NLUPipeline
from nlu.models import MotionRequest, MotionResponse

app = FastAPI(
    title="Motion Agent API",
    description="Natural Language to 3D Animation API",
    version="1.0.0"
)

# CORS middleware for frontend integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize NLU pipeline
nlu_pipeline = NLUPipeline()


class TextInput(BaseModel):
    text: str
    character_id: str = "default"


@app.get("/")
async def root():
    """Health check endpoint"""
    return {"message": "Motion Agent API is running"}


@app.post("/generate-motion", response_model=MotionResponse)
async def generate_motion(input_data: TextInput):
    """
    Generate 3D animation from natural language description
    """
    try:
        # Parse natural language input
        motion_request = MotionRequest(
            text=input_data.text,
            character_id=input_data.character_id
        )
        
        # Process through NLU pipeline
        motion_response = await nlu_pipeline.process(motion_request)
        
        return motion_response
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/health")
async def health_check():
    """Detailed health check"""
    return {
        "status": "healthy",
        "nlu_pipeline": "ready",
        "version": "1.0.0"
    }


if __name__ == "__main__":
    uvicorn.run(
        "app:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )
