from typing import List, Optional
from pydantic import BaseModel

class Action(BaseModel):
    type: str            # 动作类型，如 'flip', 'walk', 'turn', 'step'
    direction: Optional[str] = None   # 方向：forward, backward, left, right
    steps: Optional[int] = None       # 步数
    angle: Optional[int] = None       # 角度，适用于旋转、翻转等
    hand: Optional[str] = None        # 手部相关，如 left/right

class ActionSequence(BaseModel):
    sequence: List[Action]