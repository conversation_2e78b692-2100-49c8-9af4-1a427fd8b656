"""
FastAPI服务入口
Motion Agent Backend API Server
"""

from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any
import uvicorn
import sys
from loguru import logger

from nlu.nlu_pipeline import NLUPipeline
from nlu.langgraph_pipeline import LangGraphNLUPipeline
from nlu.models import MotionRequest, MotionResponse

# 导入专业动画师模块
from animation import animation_router, AnimationRequest, AnimationResponse

# Configure loguru
logger.remove()  # Remove default handler
logger.add(
    sys.stdout,
    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
    level="INFO"
)
logger.add(
    "logs/motion_agent.log",
    rotation="10 MB",
    retention="7 days",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
    level="DEBUG"
)

app = FastAPI(
    title="Professional Motion Agent API",
    description="Professional Game Animator - Natural Language to 3D Animation API",
    version="2.0.0"
)

# CORS middleware for frontend integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize NLU pipelines
nlu_pipeline = None
langgraph_pipeline = None


@app.on_event("startup")
async def startup_event():
    """Initialize services on startup"""
    global nlu_pipeline, langgraph_pipeline
    logger.info("Starting Motion Agent API server...")

    # Initialize both pipelines
    nlu_pipeline = NLUPipeline()
    langgraph_pipeline = LangGraphNLUPipeline()

    logger.success("NLU Pipelines initialized successfully")
    logger.info("Professional Motion Agent API is ready to serve requests")


class TextInput(BaseModel):
    text: str
    character_id: str = "default"


class AdvancedTextInput(BaseModel):
    text: str
    character_id: str = "default"
    use_langgraph: bool = True
    context: Optional[Dict[str, Any]] = None


@app.get("/")
async def root():
    """Health check endpoint"""
    logger.info("Root endpoint accessed")
    return {"message": "Motion Agent API is running"}


@app.post("/generate-motion", response_model=MotionResponse)
async def generate_motion(input_data: TextInput):
    """
    Generate 3D animation from natural language description
    """
    logger.info(f"Received motion generation request: {input_data.text}")
    try:
        # Parse natural language input
        motion_request = MotionRequest(
            text=input_data.text,
            character_id=input_data.character_id
        )

        logger.debug(f"Processing motion request for character: {input_data.character_id}")

        # Process through NLU pipeline
        motion_response = await nlu_pipeline.process(motion_request)

        if motion_response.success:
            logger.success(f"Successfully generated motion with {len(motion_response.action_sequence.actions)} actions")
        else:
            logger.error(f"Failed to generate motion: {motion_response.error_message}")

        return motion_response

    except Exception as e:
        logger.exception(f"Error processing motion request: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/generate-motion-advanced", response_model=MotionResponse)
async def generate_motion_advanced(input_data: AdvancedTextInput):
    """
    Generate 3D animation using advanced LangGraph pipeline
    """
    logger.info(f"Received advanced motion generation request: {input_data.text}")
    try:
        # Create motion request
        motion_request = MotionRequest(
            text=input_data.text,
            character_id=input_data.character_id,
            context=input_data.context
        )

        logger.debug(f"Using LangGraph pipeline for character: {input_data.character_id}")

        # Process through LangGraph pipeline
        motion_response = await langgraph_pipeline.process(motion_request)

        if motion_response.success:
            logger.success(f"LangGraph successfully generated motion with {len(motion_response.action_sequence.actions)} actions")
        else:
            logger.error(f"LangGraph failed to generate motion: {motion_response.error_message}")

        return motion_response

    except Exception as e:
        logger.exception(f"Error processing advanced motion request: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/health")
async def health_check():
    """Detailed health check"""
    logger.debug("Health check endpoint accessed")
    return {
        "status": "healthy",
        "nlu_pipeline": "ready" if nlu_pipeline else "not initialized",
        "langgraph_pipeline": "ready" if langgraph_pipeline else "not initialized",
        "version": "1.0.0",
        "features": {
            "langchain": True,
            "langgraph": True,
            "loguru_logging": True,
            "ruff_linting": True
        }
    }


if __name__ == "__main__":
    uvicorn.run(
        "app:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )
