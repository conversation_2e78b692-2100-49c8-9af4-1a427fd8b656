"""
游戏动画师职能模块
Game Animator Functions - Junior & Intermediate Level
"""

import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from loguru import logger
import math

from .models import (
    AnimatorAction, AnimationType, Direction, BodyPart, 
    AnimationIntensity, AnimationSequence, MotionCaptureData
)


class JuniorAnimator:
    """
    初级动画师职能
    - 动捕清理
    - 基础移动动画（走、跑、跳）
    - 循环动画
    - 简单过渡
    """
    
    def __init__(self):
        self.frame_rate = 30
        logger.info("Junior Animator initialized")
    
    def clean_motion_capture(self, mocap_data: MotionCaptureData) -> MotionCaptureData:
        """
        动捕数据清理
        - 去除噪点
        - 平滑曲线
        - 填补缺失帧
        """
        logger.info(f"Cleaning motion capture data: {mocap_data.source_file}")
        
        # 模拟噪声检测
        noise_level = self._detect_noise_level(mocap_data)
        
        # 应用滤波器
        cleaned_data = self._apply_noise_filter(mocap_data, noise_level)
        
        # 填补缺失帧
        if mocap_data.missing_frames:
            cleaned_data = self._interpolate_missing_frames(cleaned_data)
        
        # 更新状态
        cleaned_data.is_cleaned = True
        cleaned_data.noise_level = noise_level * 0.1  # 大幅降低噪声
        cleaned_data.quality_score = min(0.9, mocap_data.quality_score + 0.3)
        cleaned_data.processing_log.append("Junior animator cleaning applied")
        
        logger.success("Motion capture data cleaned successfully")
        return cleaned_data
    
    def create_walk_cycle(self, direction: Direction = Direction.FORWARD, 
                         intensity: AnimationIntensity = AnimationIntensity.NORMAL) -> AnimatorAction:
        """创建走路循环动画"""
        logger.info(f"Creating walk cycle: {direction}, {intensity}")
        
        # 基础走路参数
        base_duration = 1.0
        step_length = 0.8
        
        # 根据强度调整
        intensity_multipliers = {
            AnimationIntensity.VERY_SLOW: {"duration": 2.0, "step": 0.4},
            AnimationIntensity.SLOW: {"duration": 1.5, "step": 0.6},
            AnimationIntensity.NORMAL: {"duration": 1.0, "step": 0.8},
            AnimationIntensity.FAST: {"duration": 0.7, "step": 1.0},
            AnimationIntensity.VERY_FAST: {"duration": 0.5, "step": 1.2},
        }
        
        multiplier = intensity_multipliers.get(intensity, intensity_multipliers[AnimationIntensity.NORMAL])
        
        # 生成关键帧
        keyframes = self._generate_walk_keyframes(
            duration=base_duration * multiplier["duration"],
            step_length=step_length * multiplier["step"],
            direction=direction
        )
        
        action = AnimatorAction(
            type=AnimationType.LOCOMOTION,
            name="walk_cycle",
            direction=direction,
            intensity=intensity,
            duration=base_duration * multiplier["duration"],
            is_looping=True,
            keyframes=keyframes,
            primary_body_parts=[BodyPart.FULL_BODY],
            root_motion=True
        )
        
        return action
    
    def create_run_cycle(self, direction: Direction = Direction.FORWARD,
                        intensity: AnimationIntensity = AnimationIntensity.FAST) -> AnimatorAction:
        """创建跑步循环动画"""
        logger.info(f"Creating run cycle: {direction}, {intensity}")
        
        # 跑步比走路更快，步幅更大
        base_duration = 0.6
        step_length = 1.5
        
        intensity_multipliers = {
            AnimationIntensity.NORMAL: {"duration": 0.8, "step": 1.2},
            AnimationIntensity.FAST: {"duration": 0.6, "step": 1.5},
            AnimationIntensity.VERY_FAST: {"duration": 0.4, "step": 1.8},
            AnimationIntensity.EXPLOSIVE: {"duration": 0.3, "step": 2.0},
        }
        
        multiplier = intensity_multipliers.get(intensity, intensity_multipliers[AnimationIntensity.FAST])
        
        keyframes = self._generate_run_keyframes(
            duration=base_duration * multiplier["duration"],
            step_length=step_length * multiplier["step"],
            direction=direction
        )
        
        action = AnimatorAction(
            type=AnimationType.LOCOMOTION,
            name="run_cycle",
            direction=direction,
            intensity=intensity,
            duration=base_duration * multiplier["duration"],
            is_looping=True,
            keyframes=keyframes,
            primary_body_parts=[BodyPart.FULL_BODY],
            root_motion=True
        )
        
        return action
    
    def create_jump_animation(self, direction: Direction = Direction.UP,
                            height: float = 1.0) -> AnimatorAction:
        """创建跳跃动画"""
        logger.info(f"Creating jump animation: {direction}, height={height}")
        
        # 跳跃分为三个阶段：准备、空中、落地
        total_duration = 1.2
        
        keyframes = self._generate_jump_keyframes(height, direction, total_duration)
        
        action = AnimatorAction(
            type=AnimationType.LOCOMOTION,
            name="jump",
            direction=direction,
            intensity=AnimationIntensity.EXPLOSIVE,
            duration=total_duration,
            is_looping=False,
            keyframes=keyframes,
            primary_body_parts=[BodyPart.FULL_BODY],
            root_motion=True,
            metadata={"jump_height": height}
        )
        
        return action
    
    def create_idle_animation(self, style: str = "breathing") -> AnimatorAction:
        """创建待机动画"""
        logger.info(f"Creating idle animation: {style}")
        
        if style == "breathing":
            keyframes = self._generate_breathing_keyframes()
            duration = 4.0
        elif style == "looking_around":
            keyframes = self._generate_looking_keyframes()
            duration = 6.0
        else:
            keyframes = self._generate_basic_idle_keyframes()
            duration = 3.0
        
        action = AnimatorAction(
            type=AnimationType.IDLE,
            name=f"idle_{style}",
            intensity=AnimationIntensity.VERY_SLOW,
            duration=duration,
            is_looping=True,
            keyframes=keyframes,
            primary_body_parts=[BodyPart.UPPER_BODY],
            root_motion=False
        )
        
        return action
    
    def create_transition(self, from_action: AnimatorAction, 
                         to_action: AnimatorAction) -> AnimatorAction:
        """创建动作过渡"""
        logger.info(f"Creating transition: {from_action.name} -> {to_action.name}")
        
        transition_duration = 0.3
        
        # 简单的线性过渡
        keyframes = self._generate_transition_keyframes(
            from_action, to_action, transition_duration
        )
        
        action = AnimatorAction(
            type=AnimationType.TRANSITION,
            name=f"transition_{from_action.name}_to_{to_action.name}",
            intensity=AnimationIntensity.NORMAL,
            duration=transition_duration,
            is_looping=False,
            keyframes=keyframes,
            primary_body_parts=[BodyPart.FULL_BODY]
        )
        
        return action
    
    def _detect_noise_level(self, mocap_data: MotionCaptureData) -> float:
        """检测噪声水平"""
        # 模拟噪声检测算法
        return np.random.uniform(0.1, 0.8)
    
    def _apply_noise_filter(self, mocap_data: MotionCaptureData, noise_level: float) -> MotionCaptureData:
        """应用噪声滤波器"""
        # 模拟滤波处理
        filtered_data = mocap_data.copy()
        filtered_data.processing_log.append(f"Applied noise filter (level: {noise_level:.2f})")
        return filtered_data
    
    def _interpolate_missing_frames(self, mocap_data: MotionCaptureData) -> MotionCaptureData:
        """插值缺失帧"""
        interpolated_data = mocap_data.copy()
        interpolated_data.missing_frames = []
        interpolated_data.processing_log.append("Interpolated missing frames")
        return interpolated_data
    
    def _generate_walk_keyframes(self, duration: float, step_length: float, 
                               direction: Direction) -> Dict[str, Any]:
        """生成走路关键帧"""
        frames = int(duration * self.frame_rate)
        
        # 简化的走路关键帧数据
        keyframes = {
            "total_frames": frames,
            "root_motion": {
                "translation": self._calculate_walk_translation(step_length, direction, frames),
                "rotation": [0, 0, 0] * frames
            },
            "left_leg": self._generate_leg_cycle(frames, 0),
            "right_leg": self._generate_leg_cycle(frames, frames // 2),
            "left_arm": self._generate_arm_swing(frames, frames // 2),
            "right_arm": self._generate_arm_swing(frames, 0)
        }
        
        return keyframes
    
    def _generate_run_keyframes(self, duration: float, step_length: float,
                              direction: Direction) -> Dict[str, Any]:
        """生成跑步关键帧"""
        frames = int(duration * self.frame_rate)
        
        keyframes = {
            "total_frames": frames,
            "root_motion": {
                "translation": self._calculate_run_translation(step_length, direction, frames),
                "rotation": [0, 0, 0] * frames
            },
            "left_leg": self._generate_run_leg_cycle(frames, 0),
            "right_leg": self._generate_run_leg_cycle(frames, frames // 2),
            "left_arm": self._generate_run_arm_swing(frames, frames // 2),
            "right_arm": self._generate_run_arm_swing(frames, 0),
            "spine": self._generate_run_spine_lean(frames)
        }
        
        return keyframes
    
    def _generate_jump_keyframes(self, height: float, direction: Direction, 
                               duration: float) -> Dict[str, Any]:
        """生成跳跃关键帧"""
        frames = int(duration * self.frame_rate)
        
        # 跳跃分为三个阶段
        prep_frames = frames // 4      # 准备阶段
        air_frames = frames // 2       # 空中阶段
        land_frames = frames - prep_frames - air_frames  # 落地阶段
        
        keyframes = {
            "total_frames": frames,
            "phases": {
                "preparation": {"start": 0, "end": prep_frames},
                "airborne": {"start": prep_frames, "end": prep_frames + air_frames},
                "landing": {"start": prep_frames + air_frames, "end": frames}
            },
            "root_motion": self._generate_jump_root_motion(height, frames),
            "legs": self._generate_jump_leg_motion(frames, prep_frames, air_frames),
            "arms": self._generate_jump_arm_motion(frames)
        }
        
        return keyframes
    
    def _generate_breathing_keyframes(self) -> Dict[str, Any]:
        """生成呼吸动画关键帧"""
        frames = int(4.0 * self.frame_rate)  # 4秒循环
        
        keyframes = {
            "total_frames": frames,
            "chest": self._generate_breathing_chest(frames),
            "spine": self._generate_breathing_spine(frames)
        }
        
        return keyframes
    
    def _generate_looking_keyframes(self) -> Dict[str, Any]:
        """生成环顾动画关键帧"""
        frames = int(6.0 * self.frame_rate)
        
        keyframes = {
            "total_frames": frames,
            "head": self._generate_head_looking(frames),
            "eyes": self._generate_eye_movement(frames)
        }
        
        return keyframes
    
    def _generate_basic_idle_keyframes(self) -> Dict[str, Any]:
        """生成基础待机关键帧"""
        frames = int(3.0 * self.frame_rate)
        
        keyframes = {
            "total_frames": frames,
            "subtle_sway": self._generate_subtle_sway(frames)
        }
        
        return keyframes
    
    def _generate_transition_keyframes(self, from_action: AnimatorAction,
                                     to_action: AnimatorAction, 
                                     duration: float) -> Dict[str, Any]:
        """生成过渡关键帧"""
        frames = int(duration * self.frame_rate)
        
        keyframes = {
            "total_frames": frames,
            "blend_curve": "ease_in_out",
            "from_pose": from_action.keyframes.get("end_pose", {}),
            "to_pose": to_action.keyframes.get("start_pose", {})
        }
        
        return keyframes
    
    # 辅助方法（简化实现）
    def _calculate_walk_translation(self, step_length: float, direction: Direction, frames: int) -> List[List[float]]:
        """计算走路位移"""
        translation = []
        for i in range(frames):
            progress = i / frames
            if direction == Direction.FORWARD:
                translation.append([0, step_length * progress, 0])
            elif direction == Direction.BACKWARD:
                translation.append([0, -step_length * progress, 0])
            elif direction == Direction.LEFT:
                translation.append([-step_length * progress, 0, 0])
            elif direction == Direction.RIGHT:
                translation.append([step_length * progress, 0, 0])
            else:
                translation.append([0, 0, 0])
        return translation
    
    def _calculate_run_translation(self, step_length: float, direction: Direction, frames: int) -> List[List[float]]:
        """计算跑步位移"""
        # 跑步位移比走路更大
        return self._calculate_walk_translation(step_length * 1.5, direction, frames)
    
    def _generate_leg_cycle(self, frames: int, offset: int) -> List[List[float]]:
        """生成腿部循环动画"""
        cycle = []
        for i in range(frames):
            phase = ((i + offset) % frames) / frames * 2 * math.pi
            # 简化的腿部摆动
            rotation = [math.sin(phase) * 30, 0, 0]  # 30度摆动
            cycle.append(rotation)
        return cycle
    
    def _generate_arm_swing(self, frames: int, offset: int) -> List[List[float]]:
        """生成手臂摆动"""
        swing = []
        for i in range(frames):
            phase = ((i + offset) % frames) / frames * 2 * math.pi
            # 手臂与腿部相反摆动
            rotation = [-math.sin(phase) * 20, 0, 0]  # 20度摆动
            swing.append(rotation)
        return swing
    
    def _generate_run_leg_cycle(self, frames: int, offset: int) -> List[List[float]]:
        """生成跑步腿部动画"""
        # 跑步时腿部摆动更大
        cycle = []
        for i in range(frames):
            phase = ((i + offset) % frames) / frames * 2 * math.pi
            rotation = [math.sin(phase) * 45, 0, 0]  # 45度摆动
            cycle.append(rotation)
        return cycle
    
    def _generate_run_arm_swing(self, frames: int, offset: int) -> List[List[float]]:
        """生成跑步手臂摆动"""
        swing = []
        for i in range(frames):
            phase = ((i + offset) % frames) / frames * 2 * math.pi
            rotation = [-math.sin(phase) * 30, 0, 0]  # 30度摆动
            swing.append(rotation)
        return swing
    
    def _generate_run_spine_lean(self, frames: int) -> List[List[float]]:
        """生成跑步时的脊柱前倾"""
        lean = []
        for i in range(frames):
            # 跑步时轻微前倾
            rotation = [10, 0, 0]  # 10度前倾
            lean.append(rotation)
        return lean
    
    def _generate_jump_root_motion(self, height: float, frames: int) -> Dict[str, Any]:
        """生成跳跃根运动"""
        return {
            "height_curve": [height * math.sin(i / frames * math.pi) for i in range(frames)],
            "translation": [[0, 0, height * math.sin(i / frames * math.pi)] for i in range(frames)]
        }
    
    def _generate_jump_leg_motion(self, frames: int, prep_frames: int, air_frames: int) -> Dict[str, Any]:
        """生成跳跃腿部动作"""
        return {
            "preparation": "crouch_down",
            "takeoff": "extend_legs",
            "airborne": "tuck_legs",
            "landing": "absorb_impact"
        }
    
    def _generate_jump_arm_motion(self, frames: int) -> Dict[str, Any]:
        """生成跳跃手臂动作"""
        return {
            "swing_up": "arms_raise",
            "balance": "arms_spread"
        }
    
    def _generate_breathing_chest(self, frames: int) -> List[List[float]]:
        """生成胸部呼吸动画"""
        breathing = []
        for i in range(frames):
            phase = i / frames * 2 * math.pi
            # 胸部轻微起伏
            scale = [1.0, 1.0 + math.sin(phase) * 0.02, 1.0]
            breathing.append(scale)
        return breathing
    
    def _generate_breathing_spine(self, frames: int) -> List[List[float]]:
        """生成脊柱呼吸动画"""
        spine = []
        for i in range(frames):
            phase = i / frames * 2 * math.pi
            # 脊柱轻微伸展
            rotation = [math.sin(phase) * 2, 0, 0]  # 2度变化
            spine.append(rotation)
        return spine
    
    def _generate_head_looking(self, frames: int) -> List[List[float]]:
        """生成头部环顾动画"""
        looking = []
        for i in range(frames):
            # 头部左右环顾
            progress = i / frames
            if progress < 0.25:
                rotation = [0, progress * 4 * 30, 0]  # 向右看
            elif progress < 0.5:
                rotation = [0, 30 - (progress - 0.25) * 4 * 60, 0]  # 向左看
            elif progress < 0.75:
                rotation = [0, -30 + (progress - 0.5) * 4 * 60, 0]  # 回到右边
            else:
                rotation = [0, 30 - (progress - 0.75) * 4 * 30, 0]  # 回到中间
            looking.append(rotation)
        return looking
    
    def _generate_eye_movement(self, frames: int) -> List[List[float]]:
        """生成眼部运动"""
        # 简化的眼部运动
        return [[0, 0, 0] for _ in range(frames)]
    
    def _generate_subtle_sway(self, frames: int) -> List[List[float]]:
        """生成微妙的摇摆"""
        sway = []
        for i in range(frames):
            phase = i / frames * 2 * math.pi
            # 身体轻微摇摆
            rotation = [0, 0, math.sin(phase) * 1]  # 1度摇摆
            sway.append(rotation)
        return sway
