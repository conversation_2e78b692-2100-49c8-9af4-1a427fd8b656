# Professional Motion Agent

专业游戏动画师系统 - A Professional Game Animator System that converts natural language descriptions into high-quality 3D character animations using advanced NLU, professional animator functions, and Blender integration.

## 🎯 核心特性 Core Features

- **🧠 智能NLU**: 使用spaCy + Transformers将自然语言转换为专业动画师术语
- **👨‍🎨 专业动画师**: 实现初级和中级动画师的核心职能
- **🎬 完整流程**: 从自然语言到FBX文件的端到端解决方案
- **⚡ 高性能**: 基于FastAPI + LangChain + LangGraph的现代架构

## 🏗️ 系统架构 System Architecture

```
professional-motion-agent/
├── backend/
│   ├── app.py                          # FastAPI主服务
│   ├── animation/                      # 🎬 专业动画师模块
│   │   ├── __init__.py
│   │   ├── models.py                   # 专业动画数据模型
│   │   ├── professional_nlu.py         # 🧠 专业NLU管道 (spaCy + Transformers)
│   │   ├── animator_functions.py       # 👨‍🎨 动画师职能实现
│   │   ├── professional_pipeline.py    # 🔄 完整处理管道
│   │   └── api.py                      # 🌐 专业动画师API端点
│   ├── nlu/                           # 🔧 传统NLU模块
│   │   ├── nlu_pipeline.py            # 基础NLU管道
│   │   ├── langgraph_pipeline.py      # LangGraph增强管道
│   │   ├── models.py                  # 基础数据模型
│   │   └── utils.py                   # 工具函数
│   └── requirements.txt
├── blender_scripts/
│   └── generate_animation.py          # 🎨 专业Blender动画生成脚本
├── output/                            # 📁 输出目录
│   └── animations/                    # 生成的FBX文件
├── logs/                              # 📝 日志文件
├── test_professional_animator.py      # 🧪 系统测试脚本
├── pyproject.toml                     # Poetry配置
└── README.md
```

## 🎯 专业动画师职能 Professional Animator Functions

### 👨‍🎓 初级动画师 (Junior Animator)
- **🧹 动捕清理**: 清理动作捕捉数据的噪点和错误
- **🚶 基础移动**: 走、跑、跳等基础移动动画
- **🔄 循环动画**: Idle、呼吸等循环动画制作
- **🔗 简单过渡**: 动作之间的基本过渡

### 👨‍🎖️ 中级动画师 (Intermediate Animator)
- **⚔️ 打击感**: 攻击、受击、格挡等战斗动画
- **🤸 复杂动作**: 翻滚、后空翻720度等特技动作
- **🎭 动作融合**: 多个动作的平滑过渡和融合
- **😊 表情动画**: 面部表情和情绪表达

## 🛠️ 技术特性 Technical Features

- **🧠 智能NLU**: spaCy + Transformers + Haystack进行专业术语转换
- **🎬 专业动画**: 基于真实动画师工作流程的动画生成
- **🎨 Blender集成**: 专业级3D动画制作和FBX导出
- **⚡ 现代架构**: FastAPI + LangChain + LangGraph + Loguru
- **🔧 开发工具**: Ruff快速代码检查和格式化
- **📊 质量保证**: 动画质量评估和优化建议

## Installation

### Prerequisites

- Python 3.8+
- Blender 3.0+ (for animation generation)
- Poetry (recommended) or pip

### Using Poetry (Recommended)

```bash
# Clone the repository
git clone <repository-url>
cd motion-agent

# Install dependencies
poetry install

# Activate virtual environment
poetry shell
```

### Using pip

```bash
# Clone the repository
git clone <repository-url>
cd motion-agent

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r backend/requirements.txt
```

## Usage

### Starting the API Server

```bash
# Using Poetry
poetry run uvicorn backend.app:app --reload --host 0.0.0.0 --port 9000

# Using Python directly
cd backend
python app.py
```

The API will be available at `http://localhost:9000`

### API Documentation

Once the server is running, visit:
- Swagger UI: `http://localhost:9000/docs`
- ReDoc: `http://localhost:9000/redoc`

### 🎬 专业动画师API使用示例

```bash
# 生成复杂动作序列 - 后空翻720度 + 移动 + 转身
curl -X POST "http://localhost:9000/animation/generate" \
     -H "Content-Type: application/json" \
     -d '{
       "text": "生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步",
       "character_id": "hero_character",
       "animator_level": "intermediate",
       "quality_target": "game_ready",
       "frame_rate": 30,
       "export_format": "fbx"
     }'

# 生成战斗动作
curl -X POST "http://localhost:9000/animation/generate" \
     -H "Content-Type: application/json" \
     -d '{
       "text": "快速冲刺攻击，然后防御姿态",
       "character_id": "warrior",
       "animator_level": "intermediate"
     }'

# 生成基础移动动画
curl -X POST "http://localhost:9000/animation/generate" \
     -H "Content-Type: application/json" \
     -d '{
       "text": "慢慢走向前方，然后挥手打招呼",
       "character_id": "npc_01",
       "animator_level": "junior"
     }'
```

### Using Blender Script

```bash
# Run Blender script with motion data
blender --background --python blender_scripts/generate_animation.py -- \
        --input motion_data.json \
        --output animation.fbx \
        --format fbx
```

## Supported Actions

### Locomotion
- walk, run, jump, hop, skip, march, stride

### Poses
- sit, stand, crouch, kneel, lie, lean, bend

### Gestures
- wave, point, clap, nod, shake, bow, salute

### Interactions
- pick, grab, hold, carry, push, pull, throw, catch

### Expressions
- smile, frown, blink

## Configuration

### Environment Variables

Create a `.env` file in the project root:

```env
# API Configuration
API_HOST=0.0.0.0
API_PORT=9000
DEBUG=true

# Blender Configuration
BLENDER_PATH=/path/to/blender
DEFAULT_CHARACTER_MODEL=/path/to/character.blend

# Animation Settings
DEFAULT_FRAME_RATE=24
DEFAULT_ANIMATION_QUALITY=medium

# Logging Configuration
LOG_LEVEL=INFO
LOG_ROTATION=10 MB
LOG_RETENTION=7 days
```

### Logging

The application uses Loguru for structured logging with the following features:

- **Console Output**: Colored, formatted logs for development
- **File Logging**: Automatic log rotation and retention in `logs/motion_agent.log`
- **Log Levels**: DEBUG, INFO, WARNING, ERROR, SUCCESS
- **Structured Format**: Includes timestamp, level, module, function, and line number

Log files are automatically rotated when they reach 10MB and kept for 7 days by default.

### LangChain & LangGraph Integration

The project includes two NLU pipelines:

#### Basic NLU Pipeline (`/generate-motion`)
- Simple keyword-based action extraction
- Fast processing for basic motion commands
- Suitable for simple animations

#### Advanced LangGraph Pipeline (`/generate-motion-advanced`)
- **Structured Workflows**: Uses LangGraph for complex processing chains
- **State Management**: Maintains context throughout the processing pipeline
- **Validation**: Built-in action sequence validation
- **Error Handling**: Sophisticated error recovery and reporting
- **Extensible**: Easy to add new processing nodes

**LangGraph Workflow Steps:**
1. **Preprocess**: Text normalization and cleaning
2. **Extract Actions**: Advanced action extraction with context
3. **Validate Sequence**: Check for physical possibility and conflicts
4. **Create Sequence**: Generate final action sequence
5. **Error Handling**: Graceful error recovery

## Development

### Running Tests

```bash
# Using Poetry
poetry run pytest

# Using pip
pytest
```

### Code Formatting and Linting

```bash
# Format code with Black
poetry run black backend/

# Lint and format with Ruff (faster alternative)
poetry run ruff check backend/
poetry run ruff format backend/

# Fix issues automatically with Ruff
poetry run ruff check --fix backend/

# Lint with flake8 (legacy)
poetry run flake8 backend/

# Type checking with mypy
poetry run mypy backend/
```

### Ruff Configuration

Ruff is configured for:
- **Fast linting**: Much faster than flake8
- **Auto-fixing**: Automatically fix many issues
- **Black compatibility**: Same formatting rules as Black
- **Comprehensive rules**: Includes pycodestyle, Pyflakes, isort, and more

Configuration is in `pyproject.toml` and `.ruff.toml`.

### Pre-commit Hooks

```bash
# Install pre-commit hooks
poetry run pre-commit install

# Run hooks manually
poetry run pre-commit run --all-files
```

## API Endpoints

### POST /generate-motion

Generate animation from natural language description using the basic NLU pipeline.

**Request Body:**
```json
{
  "text": "Walk forward slowly and then wave your right hand",
  "character_id": "character_01"
}
```

### POST /generate-motion-advanced

Generate animation using the advanced LangGraph pipeline with enhanced NLP capabilities.

**Request Body:**
```json
{
  "text": "Walk forward slowly and then wave your right hand",
  "character_id": "character_01",
  "use_langgraph": true,
  "context": {
    "environment": "indoor",
    "mood": "happy"
  }
}
```

**Response:**
```json
{
  "success": true,
  "action_sequence": {
    "id": "seq_123",
    "actions": [
      {
        "id": "act_1",
        "type": "locomotion",
        "name": "walk",
        "duration": 3.0,
        "start_time": 0.0,
        "parameters": {
          "speed": "slow",
          "direction": "forward"
        }
      }
    ],
    "total_duration": 3.0,
    "character_id": "character_01"
  },
  "blender_script_path": "blender_scripts/generate_animation.py"
}
```

### GET /health

Health check endpoint.

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- FastAPI for the excellent web framework
- Blender for the powerful 3D animation capabilities
- NLTK for natural language processing tools
